import 'package:flutter/foundation.dart';

@immutable
class PreviewStore {
  final int previewStoreId;
  final String userReference;
  final int userId;
  final String previewStoreReference;
  final String previewStoreName;
  final String previewStorehandle;
  final String? previewStoreIcon;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const PreviewStore({
    required this.previewStoreId,
    required this.userReference,
    required this.userId,
    required this.previewStoreReference,
    required this.previewStoreName,
    required this.previewStorehandle,
    this.previewStoreIcon,
    this.createdAt,
    this.updatedAt,
  });

  factory PreviewStore.fromJson(Map<String, dynamic> json) {
    return PreviewStore(
      previewStoreId: json['preview_store_id'] as int? ?? 0,
      userReference: json['user_reference'] as String? ?? '',
      userId: json['user_id'] as int? ?? 0,
      previewStoreReference: json['preview_store_reference'] as String? ?? '',
      previewStoreName: json['preview_store_name'] as String? ?? '',
      previewStorehandle: json['preview_storehandle'] as String? ?? '',
      previewStoreIcon: json['preview_store_icon'] as String?,
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at']) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'preview_store_id': previewStoreId,
      'user_reference': userReference,
      'user_id': userId,
      'preview_store_reference': previewStoreReference,
      'preview_store_name': previewStoreName,
      'preview_storehandle': previewStorehandle,
      'preview_store_icon': previewStoreIcon,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}
