import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

class PreviewStoreCelebrationScreen extends StatefulWidget {
  final String previewStoreName;
  final String previewStoreHandle;
  final bool isImageSelected;
  final String? imagePath; // For mobile
  final Uint8List? webImageBytes; // For web
  
  const PreviewStoreCelebrationScreen({
    Key? key,
    required this.previewStoreName,
    required this.previewStoreHandle,
    required this.isImageSelected,
    this.imagePath,
    this.webImageBytes,
  }) : super(key: key);

  @override
  State<PreviewStoreCelebrationScreen> createState() => _PreviewStoreCelebrationScreenState();
}

class _PreviewStoreCelebrationScreenState extends State<PreviewStoreCelebrationScreen>
    with TickerProviderStateMixin {
  late AnimationController _confettiController;
  late AnimationController _iconZoomController;
  late Animation<double> _iconZoomAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controllers
    _confettiController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    );
    
    _iconZoomController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // Create zoom animation for the store icon
    _iconZoomAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _iconZoomController,
      curve: Curves.elasticOut,
    ));

    // Create fade animation for the text
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _iconZoomController,
      curve: const Interval(0.5, 1.0, curve: Curves.easeIn),
    ));

    // Start animations
    _startAnimations();
  }

  void _startAnimations() async {
    // Start confetti animation
    _confettiController.forward();
    
    // Start icon zoom animation after a short delay
    await Future.delayed(const Duration(milliseconds: 300));
    _iconZoomController.forward();
  }

  @override
  void dispose() {
    _confettiController.dispose();
    _iconZoomController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      body: SafeArea(
        child: Stack(
          children: [
            // Confetti animation
            Positioned.fill(
              child: Lottie.asset(
                AppImages.celebration,
                controller: _confettiController,
                fit: BoxFit.cover,
              ),
            ),
            
            // Main content
            Center(
              child: Padding(
                padding: const EdgeInsets.all(32.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Animated store icon
                    AnimatedBuilder(
                      animation: _iconZoomAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _iconZoomAnimation.value,
                          child: Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(
                                CommonMethods().getBorderRadius(
                                  height: 120,
                                  imageType: CustomImageContainerType.store,
                                ),
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.appBlack.withOpacity(0.2),
                                  blurRadius: 20,
                                  offset: const Offset(0, 10),
                                ),
                              ],
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(
                                CommonMethods().getBorderRadius(
                                  height: 120,
                                  imageType: CustomImageContainerType.store,
                                ),
                              ),
                              child: widget.isImageSelected
                                  ? kIsWeb && widget.webImageBytes != null
                                      // Web platform with image selected
                                      ? Image.memory(
                                          widget.webImageBytes!,
                                          fit: BoxFit.cover,
                                          width: 120,
                                          height: 120,
                                        )
                                      // Mobile platform with image selected
                                      : widget.imagePath != null
                                          ? Image.file(
                                              File(widget.imagePath!),
                                              fit: BoxFit.cover,
                                              width: 120,
                                              height: 120,
                                            )
                                          : const CustomImageContainer(
                                              width: 120,
                                              height: 120,
                                              imageUrl: AppImages.storePlaceHolder,
                                              imageType: CustomImageContainerType.store,
                                            )
                                  // No image selected
                                  : const CustomImageContainer(
                                      width: 120,
                                      height: 120,
                                      imageUrl: AppImages.storePlaceHolder,
                                      imageType: CustomImageContainerType.store,
                                    ),
                            ),
                          ),
                        );
                      },
                    ),
                    
                    const SizedBox(height: 40),
                    
                    // Animated congratulations text
                    AnimatedBuilder(
                      animation: _fadeAnimation,
                      builder: (context, child) {
                        return Opacity(
                          opacity: _fadeAnimation.value,
                          child: Column(
                            children: [
                              Text(
                                "Congratulations! 🎉",
                                style: AppTextStyle.pageHeading(textColor: AppColors.appBlack),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                "Your preview store '${widget.previewStoreName}' has been created successfully!",
                                style: AppTextStyle.contentHeading0(textColor: AppColors.writingColor2),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                "@${widget.previewStoreHandle}",
                                style: AppTextStyle.contentHeading0(textColor: AppColors.brandBlack),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 40),
                              
                              // Continue button
                              ElevatedButton(
                                onPressed: () {
                                  // Navigate back to the main screen or store list
                                  Navigator.of(context).popUntil((route) => route.isFirst);
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.brandBlack,
                                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                child: Text(
                                  "Continue",
                                  style: AppTextStyle.access0(textColor: AppColors.appWhite),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
