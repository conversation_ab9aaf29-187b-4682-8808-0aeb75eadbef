import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:swadesic/features/common_buyer_seller_screen/create_preview_store/preview_store_celebration_screen.dart';
import 'package:swadesic/features/data_model/preview_store_data_model/preview_store_data_model.dart';
import 'package:swadesic/services/preview_store_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/web_file_picker.dart';

enum PreviewStoreCreationState { Initial, Loading, Success, Error }

class CreatePreviewStoreBloc {
  final BuildContext context;
  final PreviewStoreService _previewStoreService = PreviewStoreService();

  // Text controllers
  final TextEditingController previewStoreNameCtrl = TextEditingController();
  final TextEditingController previewStoreHandleCtrl = TextEditingController();

  // Stream controllers
  final StreamController<PreviewStoreCreationState> stateCtrl = StreamController<PreviewStoreCreationState>.broadcast();
  final StreamController<bool> logoCtrl = StreamController<bool>.broadcast();
  final StreamController<bool> onTextChangeCtrl = StreamController<bool>.broadcast();

  // Image handling variables
  bool isImageSelected = false;
  XFile files = XFile("");
  
  // Web platform image variables
  Uint8List? webImageBytes;
  String? webImageName;
  String? webImageType;

  // Response data
  PreviewStoreResponse? previewStoreResponse;

  CreatePreviewStoreBloc(this.context);

  //region Init
  void init() {
    stateCtrl.sink.add(PreviewStoreCreationState.Initial);
  }
  //endregion

  //region Dispose
  void dispose() {
    previewStoreNameCtrl.dispose();
    previewStoreHandleCtrl.dispose();
    stateCtrl.close();
    logoCtrl.close();
    onTextChangeCtrl.close();
  }
  //endregion

  //region Go to Add Image Screen
  void goToAddImageScreen() async {
    if (kIsWeb) {
      // Web platform - use web file picker
      try {
        final result = await WebFilePicker.pickImage();
        if (result != null) {
          // Store the image data for web
          webImageBytes = result['bytes'];
          webImageName = result['name'];
          webImageType = result['type'];

          // Set image selected flag
          isImageSelected = true;
          logoCtrl.sink.add(true);
        }
      } catch (e) {
        debugPrint('Error picking image: $e');
      }
    } else {
      // Mobile platform - use image picker and cropper
      try {
        final picker = ImagePicker();
        final pickedFile = await picker.pickImage(
          source: ImageSource.gallery,
          imageQuality: 70,
        );

        if (pickedFile != null) {
          // Crop the image
          CroppedFile? croppedFile = await ImageCropper().cropImage(
            sourcePath: pickedFile.path,
            aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1),
            uiSettings: [
              AndroidUiSettings(
                toolbarTitle: 'Crop Image',
                toolbarColor: AppColors.brandBlack,
                toolbarWidgetColor: AppColors.appWhite,
                initAspectRatio: CropAspectRatioPreset.square,
                lockAspectRatio: true,
              ),
              IOSUiSettings(
                title: 'Crop Image',
                aspectRatioLockEnabled: true,
                resetAspectRatioEnabled: false,
              ),
            ],
          );

          if (croppedFile != null) {
            files = XFile(croppedFile.path);
            isImageSelected = true;
            logoCtrl.sink.add(true);
          }
        }
      } catch (e) {
        debugPrint('Error picking/cropping image: $e');
      }
    }
  }
  //endregion

  //region On Text Change
  void onTextChange() {
    onTextChangeCtrl.sink.add(true);
  }
  //endregion

  //region Validate Form
  bool validateForm() {
    if (previewStoreNameCtrl.text.trim().isEmpty) {
      CommonMethods.toastMessage("Please enter preview store name", context);
      return false;
    }

    if (previewStoreNameCtrl.text.trim().length > 40) {
      CommonMethods.toastMessage("Store name should be less than 40 characters", context);
      return false;
    }

    if (previewStoreHandleCtrl.text.trim().isEmpty) {
      CommonMethods.toastMessage("Please enter preview store handle", context);
      return false;
    }

    // Basic validation for store handle (alphanumeric, dashes, underscores)
    final handleRegex = RegExp(r'^[a-zA-Z0-9_-]+$');
    if (!handleRegex.hasMatch(previewStoreHandleCtrl.text.trim())) {
      CommonMethods.toastMessage("Store handle can only contain letters, numbers, dashes, and underscores", context);
      return false;
    }

    return true;
  }
  //endregion

  //region Create Preview Store
  Future<void> createPreviewStore() async {
    if (!validateForm()) {
      return;
    }

    try {
      stateCtrl.sink.add(PreviewStoreCreationState.Loading);

      if (kIsWeb) {
        // Web platform
        previewStoreResponse = await _previewStoreService.createPreviewStore(
          previewStoreName: previewStoreNameCtrl.text.trim(),
          previewStoreHandle: previewStoreHandleCtrl.text.trim(),
          previewStoreIconBytes: webImageBytes,
          previewStoreIconName: webImageName,
        );
      } else {
        // Mobile platform
        previewStoreResponse = await _previewStoreService.createPreviewStore(
          previewStoreName: previewStoreNameCtrl.text.trim(),
          previewStoreHandle: previewStoreHandleCtrl.text.trim(),
          previewStoreIcon: isImageSelected ? File(files.path) : null,
        );
      }

      stateCtrl.sink.add(PreviewStoreCreationState.Success);
      
      // Navigate to celebration screen
      goToCelebrationScreen();
      
    } catch (e) {
      stateCtrl.sink.add(PreviewStoreCreationState.Error);
      CommonMethods.toastMessage("Failed to create preview store: ${e.toString()}", context);
      debugPrint('Error creating preview store: $e');
    }
  }
  //endregion

  //region Go to Celebration Screen
  void goToCelebrationScreen() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => PreviewStoreCelebrationScreen(
          previewStoreName: previewStoreNameCtrl.text.trim(),
          previewStoreHandle: previewStoreHandleCtrl.text.trim(),
          isImageSelected: isImageSelected,
          imagePath: isImageSelected && !kIsWeb ? files.path : null,
          webImageBytes: isImageSelected && kIsWeb ? webImageBytes : null,
        ),
      ),
    );
  }
  //endregion
}
