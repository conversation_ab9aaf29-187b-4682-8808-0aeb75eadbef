

//happy
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/model/preview_store/preview_store.dart';
import 'package:swadesic/model/preview_store/preview_store_response.dart';
import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/util/app_constants.dart';

class SellerHomeService {
  // region Common Variables
  late HttpService httpService;

  // endregion

  // region | Constructor |
  SellerHomeService() {
    httpService = HttpService();
  }

  // endregion


  // region Get Seller Store
  Future<StoreListResponse> getSellerStore() async {
    // get body [for POST request]
    // endregion
    Map<String, dynamic> response;
    String url = "${AppConstants.sellerStoreList}${AppConstants.appData.userId}/";

    //#region Region - Execute Request
    response = await httpService.getApiCall(url);
    // return response;
    //print(response);

    return StoreListResponse.fromJson(response);
  }
// endregion


  // region Get Preview Stores
  Future<List<PreviewStore>> getPreviewStores() async {
    try {
      final url = "${AppConstants.previewStoreList}?user_id=${AppConstants.appData.userId}";
      final response = await httpService.getApiCall(url);
      
      final previewStoreResponse = PreviewStoreListResponse.fromJson(response);
      
      if (previewStoreResponse.message == 'success') {
        return previewStoreResponse.data;
      } else {
        throw Exception('Failed to load preview stores');
      }
    } catch (e) {
      // Handle error appropriately
      rethrow;
    }
  }
// endregion







}

