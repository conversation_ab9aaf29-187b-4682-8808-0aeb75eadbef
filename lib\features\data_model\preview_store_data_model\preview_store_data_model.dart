class PreviewStoreResponse {
  String? message;
  PreviewStoreData? data;

  PreviewStoreResponse({this.message, this.data});

  PreviewStoreResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    data = json['data'] != null ? PreviewStoreData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class PreviewStoreData {
  int? previewStoreId;
  String? userReference;
  int? userId;
  String? previewStoreReference;
  String? previewStoreName;
  String? previewStorehandle;
  String? previewStoreIcon;
  String? createdAt;
  String? updatedAt;

  PreviewStoreData({
    this.previewStoreId,
    this.userReference,
    this.userId,
    this.previewStoreReference,
    this.previewStoreName,
    this.previewStorehandle,
    this.previewStoreIcon,
    this.createdAt,
    this.updatedAt,
  });

  PreviewStoreData.fromJson(Map<String, dynamic> json) {
    previewStoreId = json['preview_store_id'];
    userReference = json['user_reference'];
    userId = json['user_id'];
    previewStoreReference = json['preview_store_reference'];
    previewStoreName = json['preview_store_name'];
    previewStorehandle = json['preview_storehandle'];
    previewStoreIcon = json['preview_store_icon'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['preview_store_id'] = previewStoreId;
    data['user_reference'] = userReference;
    data['user_id'] = userId;
    data['preview_store_reference'] = previewStoreReference;
    data['preview_store_name'] = previewStoreName;
    data['preview_storehandle'] = previewStorehandle;
    data['preview_store_icon'] = previewStoreIcon;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}
