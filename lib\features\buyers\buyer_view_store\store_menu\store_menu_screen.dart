import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/share_store_and_profile/share_store_and_profile_screen.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/features/buyers/buyer_view_store/store_details_screen/store_details_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_store/store_links/store_links.dart';


class StoreMenuScreen extends StatelessWidget {
  final StoreInfo storeInfo;

  const StoreMenuScreen({Key? key, required this.storeInfo}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: AppBar(
        title: Text('Store Menu', style: AppTextStyle.pageHeading(textColor: AppColors.appBlack)),
        backgroundColor: AppColors.appWhite,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColors.appBlack),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildAvatarCard(context),
            _buildMenuOptions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatarCard(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 16, left: 16, right: 16, bottom: 20),
      // padding: EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          colors: [
            AppColors.brandGreen.withOpacity(0.15),
            AppColors.brandGreen.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 16.0, right: 16.0, top: 16.0, bottom: 36),
            child: Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    border: Border.all(
                      color: Colors.white,
                      width: 3.5,
                    ),
                  ),
                child: CustomImageContainer(
                  imageUrl: storeInfo.icon ?? '',
                  width: 120,
                  height: 120,
                  showLevelBadge: true,
                  level: storeInfo.storeLevel,
                  badgeHeight: 40,
                  badgeWidth: 40,
                  imageType: CustomImageContainerType.store,
                  storeBadgeFontSize: 15,
                  storeBadgeBorderWidth: 2,
                ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        storeInfo.storeName ?? '',
                        style: AppTextStyle.access0(textColor: AppColors.appBlack),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 4),
                      Text(
                        '@${storeInfo.storehandle ?? ''}',
                        style: AppTextStyle.access0(textColor: AppColors.writingBlack1),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            bottom: 10,
            right: 10,
            child: InkWell(
              onTap: () {
                _showQRCode(context);
              },
              child: Container(
                padding: EdgeInsets.all(0),
                // decoration: const BoxDecoration(
                //   color: AppColors.appWhite,
                //   boxShadow: [
                //     BoxShadow(
                //       color: Colors.black12,
                //       blurRadius: 4,
                //       offset: Offset(0, 2),
                //     ),
                //   ],
                // ),
                child: SvgPicture.asset(
                  AppImages.storeMenuQrCodeIcon,
                  width: 28,  
                  height: 28,
                  color: AppColors.appBlack,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showQRCode(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ShareStoreAndProfileScreen(
          storeInfo: storeInfo,
        ),
      ),
    );
  }

  Widget _buildMenuOptions(BuildContext context) {
    final List<Map<String, dynamic>> options = [
      {
        'icon': AppImages.storeMenuLinkIcon, 
        'title': 'Links',
        'onTap': ()  async {
              //If link is not empty
              if (storeInfo.storelinks!.isNotEmpty) {
                await CommonMethods.appMinimumBottomSheets(
                    bottomSheetName: CommonMethods.singularPluralText(
                        item: storeInfo.storelinks!.length,
                        isValueReturn: false,
                        singular: AppStrings.link,
                        plural: AppStrings.links),
                    screen: StoreLinks(
                      storeLinks: storeInfo.storelinks!,
                      previousScreenContext: context,
                    ),
                    context: context);
              } else {
                //if no link are there
                CommonMethods.toastMessage(
                    AppStrings.thereAreNoLinksToShare, context);
              }
            },
      },
      {
        'icon': AppImages.supportStoreNoFill, 
        'title': 'Support',
        'onTap': () {
           CommonMethods.toastMessage(
                    "This feature is coming soon", context);
        },
      },
      {
        'icon': AppImages.faqIcon, 
        'title': 'FAQs(Coming Soon)',
        'onTap': () {
           CommonMethods.toastMessage(
                    "This feature is coming soon", context);
        },
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 0.9,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: options.length,
      itemBuilder: (context, index) {
        return _buildMenuOption(
          icon: options[index]['icon']! as String,
          title: options[index]['title']! as String,
          onTap: options[index]['onTap']! as VoidCallback,
        );
      },
    );
  }

  Widget _buildMenuOption({
    required String icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            color: AppColors.appWhite,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                icon,
                width: 28,
                height: 28,
                // color: AppColors.appBlack,
              ),
              SizedBox(height: 12),
              Text(
                title,
                style: AppTextStyle.access0(
                  textColor: AppColors.appBlack,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
